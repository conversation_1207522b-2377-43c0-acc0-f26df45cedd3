// 僵尸基类
class Zombie {
    constructor(x, y, row) {
        this.x = x;
        this.y = y;
        this.row = row;
        this.width = 60;
        this.height = 80;
        this.health = 100;
        this.maxHealth = 100;
        this.speed = 20; // 像素/秒
        this.damage = 50;
        this.attackRate = 1; // 每秒攻击次数
        this.lastAttackTime = 0;
        this.isEating = false;
        this.target = null;
        this.shouldDestroy = false;
        this.animationFrame = 0;
        this.animationSpeed = 0.15;
        this.type = 'normal';
        
        // 状态
        this.state = 'walking'; // walking, eating, dying
        this.stateTime = 0;
    }
    
    update(deltaTime) {
        this.stateTime += deltaTime;
        this.animationFrame += this.animationSpeed;
        
        switch (this.state) {
            case 'walking':
                this.updateWalking(deltaTime);
                break;
            case 'eating':
                this.updateEating(deltaTime);
                break;
            case 'dying':
                this.updateDying(deltaTime);
                break;
        }
        
        // 检查是否到达房子
        if (this.x <= -this.width) {
            game.zombieReachedHouse();
            this.shouldDestroy = true;
        }
        
        // 检查是否死亡
        if (this.health <= 0 && this.state !== 'dying') {
            this.state = 'dying';
            this.stateTime = 0;
        }
    }
    
    updateWalking(deltaTime) {
        // 检查是否有植物阻挡
        const plant = this.checkForPlant();
        if (plant) {
            this.target = plant;
            this.state = 'eating';
            this.stateTime = 0;
            return;
        }
        
        // 继续向左移动
        this.x -= this.speed * deltaTime;
    }
    
    updateEating(deltaTime) {
        if (!this.target || this.target.shouldDestroy) {
            // 目标消失，继续行走
            this.target = null;
            this.state = 'walking';
            this.stateTime = 0;
            return;
        }
        
        // 攻击植物
        this.lastAttackTime += deltaTime;
        if (this.lastAttackTime >= 1 / this.attackRate) {
            this.attackPlant(this.target);
            this.lastAttackTime = 0;
        }
    }
    
    updateDying(deltaTime) {
        // 死亡动画持续1秒
        if (this.stateTime >= 1) {
            this.shouldDestroy = true;
            
            // 添加死亡粒子效果
            for (let i = 0; i < 10; i++) {
                const particle = createParticle(
                    this.x + this.width/2 + random(-20, 20),
                    this.y + this.height/2 + random(-20, 20),
                    '#8B0000',
                    random(3, 8),
                    { x: random(-2, 2), y: random(-3, -1) }
                );
                game.engine.addParticle(particle);
            }
        }
    }
    
    checkForPlant() {
        // 检查当前位置是否有植物
        const plants = game.getPlantsInRow(this.row);
        for (let plant of plants) {
            if (this.x <= plant.x + plant.width && 
                this.x + this.width >= plant.x) {
                return plant;
            }
        }
        return null;
    }
    
    attackPlant(plant) {
        plant.takeDamage(this.damage);
        
        // 添加攻击粒子效果
        for (let i = 0; i < 3; i++) {
            const particle = createParticle(
                plant.x + random(0, plant.width),
                plant.y + random(0, plant.height),
                '#ff0000',
                random(2, 5),
                { x: random(-1, 1), y: random(-2, 0) }
            );
            game.engine.addParticle(particle);
        }
    }
    
    takeDamage(damage) {
        this.health -= damage;
        if (this.health < 0) this.health = 0;
        
        // 受伤效果
        for (let i = 0; i < 2; i++) {
            const particle = createParticle(
                this.x + this.width/2 + random(-10, 10),
                this.y + this.height/2 + random(-10, 10),
                '#ff0000',
                random(2, 4),
                { x: random(-1, 1), y: random(-2, 0) }
            );
            game.engine.addParticle(particle);
        }
    }
    
    render(ctx) {
        // 绘制僵尸阴影
        ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        ctx.ellipse(this.x + this.width/2, this.y + this.height + 5, 
                   this.width/2, 8, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // 根据状态调整透明度
        let alpha = 1;
        if (this.state === 'dying') {
            alpha = 1 - (this.stateTime / 1);
        }
        
        ctx.save();
        ctx.globalAlpha = alpha;
        
        this.renderZombie(ctx);
        
        // 绘制生命值条
        if (this.health < this.maxHealth && this.state !== 'dying') {
            this.renderHealthBar(ctx);
        }
        
        ctx.restore();
    }
    
    renderZombie(ctx) {
        // 子类实现具体的僵尸绘制
    }
    
    renderHealthBar(ctx) {
        const barWidth = this.width;
        const barHeight = 4;
        const barX = this.x;
        const barY = this.y - 10;
        
        // 背景
        ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
        ctx.fillRect(barX, barY, barWidth, barHeight);
        
        // 生命值
        ctx.fillStyle = 'rgba(255, 255, 0, 0.8)';
        const healthWidth = (this.health / this.maxHealth) * barWidth;
        ctx.fillRect(barX, barY, healthWidth, barHeight);
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// 普通僵尸类
class NormalZombie extends Zombie {
    constructor(x, y, row) {
        super(x, y, row);
        this.type = 'normal';
        this.health = 100;
        this.maxHealth = 100;
        this.speed = 20;
        this.damage = 50;
    }
    
    renderZombie(ctx) {
        const centerX = this.x + this.width/2;
        const centerY = this.y + this.height/2;
        
        // 身体
        ctx.fillStyle = '#8FBC8F';
        ctx.fillRect(this.x + 15, this.y + 30, 30, 40);
        
        // 头部
        ctx.fillStyle = '#9ACD32';
        ctx.beginPath();
        ctx.arc(centerX, this.y + 20, 18, 0, Math.PI * 2);
        ctx.fill();
        
        // 眼睛
        ctx.fillStyle = '#FF0000';
        ctx.beginPath();
        ctx.arc(centerX - 6, this.y + 15, 3, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(centerX + 6, this.y + 15, 3, 0, Math.PI * 2);
        ctx.fill();
        
        // 嘴巴
        ctx.fillStyle = '#000000';
        ctx.fillRect(centerX - 8, this.y + 22, 16, 4);
        
        // 牙齿
        ctx.fillStyle = '#FFFFFF';
        for (let i = 0; i < 4; i++) {
            ctx.fillRect(centerX - 6 + i * 3, this.y + 22, 2, 6);
        }
        
        // 手臂
        ctx.fillStyle = '#8FBC8F';
        // 左臂
        const leftArmOffset = Math.sin(this.animationFrame) * 5;
        ctx.fillRect(this.x + 5, this.y + 35 + leftArmOffset, 15, 8);
        // 右臂
        const rightArmOffset = Math.sin(this.animationFrame + Math.PI) * 5;
        ctx.fillRect(this.x + 40, this.y + 35 + rightArmOffset, 15, 8);
        
        // 腿部
        ctx.fillStyle = '#556B2F';
        // 左腿
        const leftLegOffset = Math.sin(this.animationFrame * 2) * 3;
        ctx.fillRect(this.x + 18, this.y + 65 + leftLegOffset, 8, 15);
        // 右腿
        const rightLegOffset = Math.sin(this.animationFrame * 2 + Math.PI) * 3;
        ctx.fillRect(this.x + 34, this.y + 65 + rightLegOffset, 8, 15);
        
        // 破烂的衣服效果
        ctx.fillStyle = '#654321';
        ctx.fillRect(this.x + 20, this.y + 45, 20, 3);
        ctx.fillRect(this.x + 25, this.y + 55, 10, 2);
    }
}

// 路障僵尸类
class ConeheadZombie extends Zombie {
    constructor(x, y, row) {
        super(x, y, row);
        this.type = 'conehead';
        this.health = 200;
        this.maxHealth = 200;
        this.speed = 18;
        this.damage = 50;
        this.hasHelmet = true;
    }
    
    takeDamage(damage) {
        super.takeDamage(damage);
        
        // 当生命值低于一半时，头盔掉落
        if (this.health < this.maxHealth * 0.5) {
            this.hasHelmet = false;
        }
    }
    
    renderZombie(ctx) {
        const centerX = this.x + this.width/2;
        const centerY = this.y + this.height/2;
        
        // 身体
        ctx.fillStyle = '#8FBC8F';
        ctx.fillRect(this.x + 15, this.y + 30, 30, 40);
        
        // 头部
        ctx.fillStyle = '#9ACD32';
        ctx.beginPath();
        ctx.arc(centerX, this.y + 20, 18, 0, Math.PI * 2);
        ctx.fill();
        
        // 路障头盔
        if (this.hasHelmet) {
            ctx.fillStyle = '#FF6347';
            ctx.beginPath();
            ctx.moveTo(centerX, this.y + 2);
            ctx.lineTo(centerX - 15, this.y + 25);
            ctx.lineTo(centerX + 15, this.y + 25);
            ctx.closePath();
            ctx.fill();
            
            // 头盔条纹
            ctx.fillStyle = '#FFFFFF';
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(centerX - 10 + i * 7, this.y + 15 + i * 3, 6, 2);
            }
        }
        
        // 眼睛
        ctx.fillStyle = '#FF0000';
        ctx.beginPath();
        ctx.arc(centerX - 6, this.y + 15, 3, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(centerX + 6, this.y + 15, 3, 0, Math.PI * 2);
        ctx.fill();
        
        // 嘴巴
        ctx.fillStyle = '#000000';
        ctx.fillRect(centerX - 8, this.y + 22, 16, 4);
        
        // 牙齿
        ctx.fillStyle = '#FFFFFF';
        for (let i = 0; i < 4; i++) {
            ctx.fillRect(centerX - 6 + i * 3, this.y + 22, 2, 6);
        }
        
        // 手臂
        ctx.fillStyle = '#8FBC8F';
        const leftArmOffset = Math.sin(this.animationFrame) * 5;
        ctx.fillRect(this.x + 5, this.y + 35 + leftArmOffset, 15, 8);
        const rightArmOffset = Math.sin(this.animationFrame + Math.PI) * 5;
        ctx.fillRect(this.x + 40, this.y + 35 + rightArmOffset, 15, 8);
        
        // 腿部
        ctx.fillStyle = '#556B2F';
        const leftLegOffset = Math.sin(this.animationFrame * 2) * 3;
        ctx.fillRect(this.x + 18, this.y + 65 + leftLegOffset, 8, 15);
        const rightLegOffset = Math.sin(this.animationFrame * 2 + Math.PI) * 3;
        ctx.fillRect(this.x + 34, this.y + 65 + rightLegOffset, 8, 15);
    }
}
