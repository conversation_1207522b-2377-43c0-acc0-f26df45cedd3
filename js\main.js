// 主入口文件
document.addEventListener('DOMContentLoaded', async () => {
    console.log('植物大战僵尸 - 网页版启动中...');
    
    try {
        // 创建游戏实例
        game = new Game();
        
        // 初始化游戏
        await game.initialize();
        
        console.log('游戏初始化完成！');
        
        // 添加键盘快捷键
        setupKeyboardShortcuts();
        
        // 添加调试功能
        setupDebugFeatures();
        
    } catch (error) {
        console.error('游戏初始化失败:', error);
        showErrorMessage('游戏加载失败，请刷新页面重试。');
    }
});

// 设置键盘快捷键
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        if (!game || game.gameOver) return;
        
        switch (e.code) {
            case 'Space':
                e.preventDefault();
                game.togglePause();
                break;
            case 'KeyR':
                e.preventDefault();
                if (e.ctrlKey) {
                    game.restartGame();
                }
                break;
            case 'Digit1':
                e.preventDefault();
                game.selectPlant('sunflower');
                break;
            case 'Digit2':
                e.preventDefault();
                game.selectPlant('peashooter');
                break;
            case 'Digit3':
                e.preventDefault();
                game.selectPlant('wallnut');
                break;
            case 'Escape':
                e.preventDefault();
                // 取消植物选择
                game.selectedPlant = null;
                document.querySelectorAll('.plant-card').forEach(card => {
                    card.classList.remove('selected');
                });
                break;
            case 'KeyD':
                if (e.ctrlKey) {
                    e.preventDefault();
                    toggleDebugMode();
                }
                break;
        }
    });
}

// 设置调试功能
function setupDebugFeatures() {
    // 调试模式切换
    window.toggleDebugMode = function() {
        if (game && game.engine) {
            game.engine.showDebugInfo = !game.engine.showDebugInfo;
            console.log('调试模式:', game.engine.showDebugInfo ? '开启' : '关闭');
        }
    };
    
    // 添加阳光（调试用）
    window.addSun = function(amount = 100) {
        if (game) {
            game.addSun(amount);
            console.log(`添加了 ${amount} 阳光`);
        }
    };
    
    // 生成僵尸（调试用）
    window.spawnZombie = function(type = 'normal', row = 0) {
        if (game) {
            const x = game.engine.canvas.width + 50;
            const y = row * game.gridHeight + game.gridOffsetY + 60;
            
            let zombie;
            switch (type) {
                case 'conehead':
                    zombie = new ConeheadZombie(x, y, row);
                    break;
                default:
                    zombie = new NormalZombie(x, y, row);
                    break;
            }
            
            game.zombies.push(zombie);
            game.engine.addGameObject(zombie);
            console.log(`生成了一个 ${type} 僵尸在第 ${row} 行`);
        }
    };
    
    // 清除所有僵尸（调试用）
    window.clearZombies = function() {
        if (game) {
            game.zombies.forEach(zombie => {
                zombie.shouldDestroy = true;
            });
            console.log('清除了所有僵尸');
        }
    };
    
    // 跳到下一波（调试用）
    window.nextWave = function() {
        if (game) {
            game.zombiesSpawned = game.zombiesInWave;
            game.zombies.forEach(zombie => {
                zombie.shouldDestroy = true;
            });
            console.log('跳到下一波');
        }
    };
    
    // 显示调试信息
    console.log('调试命令:');
    console.log('- toggleDebugMode(): 切换调试模式');
    console.log('- addSun(amount): 添加阳光');
    console.log('- spawnZombie(type, row): 生成僵尸');
    console.log('- clearZombies(): 清除所有僵尸');
    console.log('- nextWave(): 跳到下一波');
    console.log('');
    console.log('键盘快捷键:');
    console.log('- 空格键: 暂停/继续');
    console.log('- Ctrl+R: 重新开始');
    console.log('- 1,2,3: 选择植物');
    console.log('- ESC: 取消选择');
    console.log('- Ctrl+D: 切换调试模式');
}

// 显示错误消息
function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 0, 0, 0.9);
        color: white;
        padding: 20px;
        border-radius: 10px;
        font-size: 18px;
        text-align: center;
        z-index: 9999;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    `;
    errorDiv.textContent = message;
    document.body.appendChild(errorDiv);
    
    // 5秒后自动移除
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

// 性能监控
function setupPerformanceMonitoring() {
    let frameCount = 0;
    let lastTime = performance.now();
    
    function updateFPS() {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
            const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
            
            // 如果FPS过低，显示警告
            if (fps < 30) {
                console.warn(`性能警告: FPS = ${fps}`);
            }
            
            frameCount = 0;
            lastTime = currentTime;
        }
        
        requestAnimationFrame(updateFPS);
    }
    
    updateFPS();
}

// 启动性能监控
setupPerformanceMonitoring();

// 处理页面可见性变化
document.addEventListener('visibilitychange', () => {
    if (game && game.gameStarted && !game.gameOver) {
        if (document.hidden) {
            // 页面隐藏时自动暂停
            if (!game.isPaused) {
                game.togglePause();
            }
        }
    }
});

// 处理窗口大小变化
window.addEventListener('resize', debounce(() => {
    if (game && game.engine && game.engine.canvas) {
        // 这里可以添加响应式调整逻辑
        console.log('窗口大小已改变');
    }
}, 250));

// 防止页面刷新时丢失游戏进度（可选）
window.addEventListener('beforeunload', (e) => {
    if (game && game.gameStarted && !game.gameOver && game.wave > 1) {
        e.preventDefault();
        e.returnValue = '确定要离开吗？游戏进度将会丢失。';
        return e.returnValue;
    }
});

// 导出全局函数供HTML使用
window.game = null; // 将在初始化后设置
