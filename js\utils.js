// 工具函数集合

// 获取两点之间的距离
function getDistance(x1, y1, x2, y2) {
    return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
}

// 检查两个矩形是否碰撞
function checkCollision(rect1, rect2) {
    return rect1.x < rect2.x + rect2.width &&
           rect1.x + rect1.width > rect2.x &&
           rect1.y < rect2.y + rect2.height &&
           rect1.y + rect1.height > rect2.y;
}

// 检查点是否在矩形内
function isPointInRect(x, y, rect) {
    return x >= rect.x && x <= rect.x + rect.width &&
           y >= rect.y && y <= rect.y + rect.height;
}

// 将屏幕坐标转换为网格坐标
function screenToGrid(x, y) {
    const gridWidth = 100;  // 每个网格的宽度
    const gridHeight = 120; // 每个网格的高度
    const offsetX = 0;      // X轴偏移
    const offsetY = 0;      // Y轴偏移
    
    const col = Math.floor((x - offsetX) / gridWidth);
    const row = Math.floor((y - offsetY) / gridHeight);
    
    return {
        col: Math.max(0, Math.min(8, col)), // 限制在0-8列
        row: Math.max(0, Math.min(4, row))  // 限制在0-4行
    };
}

// 将网格坐标转换为屏幕坐标
function gridToScreen(col, row) {
    const gridWidth = 100;
    const gridHeight = 120;
    const offsetX = 0;
    const offsetY = 0;
    
    return {
        x: col * gridWidth + offsetX + gridWidth / 2,
        y: row * gridHeight + offsetY + gridHeight / 2
    };
}

// 生成随机数
function random(min, max) {
    return Math.random() * (max - min) + min;
}

// 生成随机整数
function randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 角度转弧度
function degToRad(degrees) {
    return degrees * (Math.PI / 180);
}

// 弧度转角度
function radToDeg(radians) {
    return radians * (180 / Math.PI);
}

// 限制数值在指定范围内
function clamp(value, min, max) {
    return Math.min(Math.max(value, min), max);
}

// 线性插值
function lerp(start, end, factor) {
    return start + (end - start) * factor;
}

// 创建简单的动画缓动函数
function easeInOut(t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
}

// 格式化数字显示（添加千位分隔符）
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 深拷贝对象
function deepClone(obj) {
    if (obj === null || typeof obj !== "object") return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === "object") {
        const clonedObj = {};
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 加载图片资源
function loadImage(src) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = src;
    });
}

// 加载音频资源
function loadAudio(src) {
    return new Promise((resolve, reject) => {
        const audio = new Audio();
        audio.oncanplaythrough = () => resolve(audio);
        audio.onerror = reject;
        audio.src = src;
    });
}

// 播放音效
function playSound(audioElement, volume = 1) {
    if (audioElement) {
        audioElement.volume = volume;
        audioElement.currentTime = 0;
        audioElement.play().catch(e => console.log('音频播放失败:', e));
    }
}

// 创建粒子效果
function createParticle(x, y, color = '#ffff00', size = 5, velocity = { x: 0, y: -2 }) {
    return {
        x: x,
        y: y,
        color: color,
        size: size,
        velocity: velocity,
        life: 1.0,
        decay: 0.02
    };
}

// 更新粒子
function updateParticle(particle) {
    particle.x += particle.velocity.x;
    particle.y += particle.velocity.y;
    particle.life -= particle.decay;
    particle.size *= 0.98;
    return particle.life > 0;
}

// 绘制粒子
function drawParticle(ctx, particle) {
    ctx.save();
    ctx.globalAlpha = particle.life;
    ctx.fillStyle = particle.color;
    ctx.beginPath();
    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
    ctx.fill();
    ctx.restore();
}
