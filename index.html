<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>植物大战僵尸 - 网页版</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏标题 -->
        <div id="gameTitle">
            <h1>植物大战僵尸</h1>
            <p>网页版</p>
        </div>

        <!-- 游戏界面 -->
        <div id="gameUI">
            <!-- 阳光计数器 -->
            <div id="sunCounter">
                <img src="images/sun.png" alt="阳光" id="sunIcon">
                <span id="sunAmount">999999</span>
            </div>

            <!-- 植物选择栏 -->
            <div id="plantSelector">
                <div class="plant-card" data-plant="sunflower" data-cost="50">
                    <img src="images/sunflower.png" alt="向日葵">
                    <span class="cost">50</span>
                </div>
                <div class="plant-card" data-plant="peashooter" data-cost="100">
                    <img src="images/peashooter.png" alt="豌豆射手">
                    <span class="cost">100</span>
                </div>
                <div class="plant-card" data-plant="wallnut" data-cost="50">
                    <img src="images/wallnut.png" alt="坚果墙">
                    <span class="cost">50</span>
                </div>
            </div>
        </div>

        <!-- 游戏画布 -->
        <canvas id="gameCanvas" width="900" height="600"></canvas>

        <!-- 游戏控制按钮 -->
        <div id="gameControls">
            <button id="pauseBtn">暂停</button>
            <button id="restartBtn">重新开始</button>
        </div>

        <!-- 游戏结束界面 -->
        <div id="gameOverScreen" class="hidden">
            <div class="game-over-content">
                <h2 id="gameOverTitle">游戏结束</h2>
                <p id="gameOverMessage">僵尸吃掉了你的脑子！</p>
                <button id="playAgainBtn">再玩一次</button>
            </div>
        </div>
    </div>

    <!-- 音频文件 -->
    <audio id="bgMusic" loop>
        <source src="audio/background.mp3" type="audio/mpeg">
    </audio>
    <audio id="plantSound">
        <source src="audio/plant.mp3" type="audio/mpeg">
    </audio>
    <audio id="shootSound">
        <source src="audio/shoot.mp3" type="audio/mpeg">
    </audio>
    <audio id="zombieSound">
        <source src="audio/zombie.mp3" type="audio/mpeg">
    </audio>

    <!-- JavaScript 文件 -->
    <script src="js/utils.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/plant.js"></script>
    <script src="js/zombie.js"></script>
    <script src="js/bullet.js"></script>
    <script src="js/sun.js"></script>
    <script src="js/game.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
