// 主游戏类
class Game {
    constructor() {
        this.engine = new GameEngine('gameCanvas');
        this.isInitialized = false;
        
        // 游戏状态
        this.sunAmount = 999999;
        this.selectedPlant = null;
        this.gameStarted = false;
        this.gameOver = false;
        this.isPaused = false;
        this.score = 0;
        this.wave = 1;
        this.zombiesKilled = 0;
        
        // 游戏网格 (9列 x 5行)
        this.gridCols = 9;
        this.gridRows = 5;
        this.gridWidth = 100;
        this.gridHeight = 120;
        this.gridOffsetX = 0;
        this.gridOffsetY = 0;
        
        // 游戏对象数组
        this.plants = [];
        this.zombies = [];
        this.bullets = [];
        this.suns = [];
        
        // 植物网格 (用于快速查找)
        this.plantGrid = [];
        for (let row = 0; row < this.gridRows; row++) {
            this.plantGrid[row] = [];
            for (let col = 0; col < this.gridCols; col++) {
                this.plantGrid[row][col] = null;
            }
        }
        
        // 僵尸生成
        this.zombieSpawnTimer = 0;
        this.zombieSpawnRate = 5; // 每5秒生成一个僵尸
        this.zombieSpawnRateDecrease = 0.1; // 每波减少0.1秒
        this.minZombieSpawnRate = 1; // 最小生成间隔
        
        // 阳光生成
        this.naturalSunTimer = 0;
        this.naturalSunRate = 10; // 每10秒天空掉落一个阳光
        
        // 波次管理
        this.zombiesInWave = 10;
        this.zombiesSpawned = 0;
        this.waveCompleted = false;
        this.waveDelay = 5; // 波次间隔
        this.waveTimer = 0;
        
        // 植物成本
        this.plantCosts = {
            sunflower: 50,
            peashooter: 100,
            wallnut: 50
        };
        
        // 植物冷却时间
        this.plantCooldowns = {
            sunflower: 7.5,
            peashooter: 7.5,
            wallnut: 30
        };
        
        this.plantLastUsed = {
            sunflower: -999,
            peashooter: -999,
            wallnut: -999
        };
        
        this.initializeUI();
    }
    
    async initialize() {
        if (this.isInitialized) return;
        
        // 加载资源
        await this.loadResources();
        
        // 初始化游戏引擎
        this.engine.start();
        
        // 设置游戏循环回调
        const originalUpdate = this.engine.update.bind(this.engine);
        this.engine.update = (deltaTime) => {
            originalUpdate(deltaTime);
            this.update(deltaTime);
        };
        
        const originalRender = this.engine.render.bind(this.engine);
        this.engine.render = () => {
            this.render(this.engine.ctx);
            originalRender();
        };
        
        this.isInitialized = true;
        this.startGame();
    }
    
    async loadResources() {
        // 这里可以加载图片和音频资源
        // 由于我们使用代码绘制，暂时跳过图片加载
        
        // 创建占位音频
        this.engine.sounds.set('shoot', null);
        this.engine.sounds.set('plant', null);
        this.engine.sounds.set('zombie', null);
        this.engine.sounds.set('collectSun', null);
        this.engine.sounds.set('gameOver', null);
    }
    
    initializeUI() {
        // 植物选择卡片点击事件
        const plantCards = document.querySelectorAll('.plant-card');
        plantCards.forEach(card => {
            card.addEventListener('click', () => {
                const plantType = card.dataset.plant;
                this.selectPlant(plantType);
            });
        });
        
        // 游戏控制按钮
        document.getElementById('pauseBtn').addEventListener('click', () => {
            this.togglePause();
        });
        
        document.getElementById('restartBtn').addEventListener('click', () => {
            this.restartGame();
        });
        
        document.getElementById('playAgainBtn').addEventListener('click', () => {
            this.restartGame();
        });
        
        // 画布点击事件
        this.engine.canvas.addEventListener('click', (e) => {
            this.handleCanvasClick(e);
        });
        
        // 更新UI
        this.updateUI();
    }
    
    selectPlant(plantType) {
        if (!this.canAffordPlant(plantType) || !this.isPlantReady(plantType)) {
            return;
        }
        
        // 更新选中状态
        document.querySelectorAll('.plant-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        const selectedCard = document.querySelector(`[data-plant="${plantType}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
            this.selectedPlant = plantType;
        }
    }
    
    handleCanvasClick(e) {
        if (!this.selectedPlant || this.gameOver || this.isPaused) return;
        
        const rect = this.engine.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const gridPos = screenToGrid(x, y);
        this.tryPlantAt(gridPos.col, gridPos.row);
    }
    
    tryPlantAt(col, row) {
        if (!this.selectedPlant) return false;
        
        // 检查网格位置是否有效
        if (col < 0 || col >= this.gridCols || row < 0 || row >= this.gridRows) {
            return false;
        }
        
        // 检查位置是否已有植物
        if (this.plantGrid[row][col] !== null) {
            return false;
        }
        
        // 检查是否有足够的阳光
        if (!this.canAffordPlant(this.selectedPlant)) {
            return false;
        }
        
        // 检查植物是否冷却完毕
        if (!this.isPlantReady(this.selectedPlant)) {
            return false;
        }
        
        // 种植植物
        this.plantAt(col, row, this.selectedPlant);
        return true;
    }
    
    plantAt(col, row, plantType) {
        const screenPos = gridToScreen(col, row);
        let plant;
        
        switch (plantType) {
            case 'sunflower':
                plant = new Sunflower(screenPos.x - 40, screenPos.y - 40);
                break;
            case 'peashooter':
                plant = new Peashooter(screenPos.x - 40, screenPos.y - 40);
                break;
            case 'wallnut':
                plant = new Wallnut(screenPos.x - 40, screenPos.y - 40);
                break;
            default:
                return;
        }
        
        // 扣除阳光
        this.sunAmount -= this.plantCosts[plantType];
        
        // 设置冷却时间
        this.plantLastUsed[plantType] = Date.now() / 1000;
        
        // 添加到游戏中
        this.plants.push(plant);
        this.plantGrid[row][col] = plant;
        this.engine.addGameObject(plant);
        
        // 清除选择
        this.selectedPlant = null;
        document.querySelectorAll('.plant-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // 更新UI
        this.updateUI();
        
        // 播放种植音效
        this.engine.playSound('plant', 0.5);
    }
    
    canAffordPlant(plantType) {
        return this.sunAmount >= this.plantCosts[plantType];
    }
    
    isPlantReady(plantType) {
        const currentTime = Date.now() / 1000;
        const lastUsed = this.plantLastUsed[plantType];
        const cooldown = this.plantCooldowns[plantType];
        return currentTime - lastUsed >= cooldown;
    }
    
    addSun(amount) {
        this.sunAmount += amount;
        this.updateUI();
    }
    
    startGame() {
        this.gameStarted = true;
        this.gameOver = false;
        this.engine.setGameState('playing');
    }
    
    togglePause() {
        this.isPaused = !this.isPaused;
        if (this.isPaused) {
            this.engine.pause();
            document.getElementById('pauseBtn').textContent = '继续';
        } else {
            this.engine.resume();
            document.getElementById('pauseBtn').textContent = '暂停';
        }
    }
    
    restartGame() {
        // 重置游戏状态
        this.sunAmount = 999999;
        this.selectedPlant = null;
        this.gameStarted = false;
        this.gameOver = false;
        this.isPaused = false;
        this.score = 0;
        this.wave = 1;
        this.zombiesKilled = 0;
        this.zombiesSpawned = 0;
        this.waveCompleted = false;
        this.zombieSpawnTimer = 0;
        this.naturalSunTimer = 0;
        this.waveTimer = 0;
        
        // 清空游戏对象
        this.plants = [];
        this.zombies = [];
        this.bullets = [];
        this.suns = [];
        
        // 重置植物网格
        for (let row = 0; row < this.gridRows; row++) {
            for (let col = 0; col < this.gridCols; col++) {
                this.plantGrid[row][col] = null;
            }
        }
        
        // 重置植物冷却
        for (let plantType in this.plantLastUsed) {
            this.plantLastUsed[plantType] = -999;
        }
        
        // 清空游戏引擎对象
        this.engine.clearGameObjects();
        
        // 隐藏游戏结束界面
        document.getElementById('gameOverScreen').classList.add('hidden');
        
        // 重置UI
        document.getElementById('pauseBtn').textContent = '暂停';
        document.querySelectorAll('.plant-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        this.updateUI();
        this.startGame();
    }

    update(deltaTime) {
        if (this.gameOver || this.isPaused || !this.gameStarted) return;

        // 更新僵尸生成
        this.updateZombieSpawning(deltaTime);

        // 更新天然阳光生成
        this.updateNaturalSunGeneration(deltaTime);

        // 更新波次管理
        this.updateWaveManagement(deltaTime);

        // 清理已销毁的对象
        this.cleanupDestroyedObjects();

        // 更新UI
        this.updateUI();
    }

    updateZombieSpawning(deltaTime) {
        if (this.zombiesSpawned >= this.zombiesInWave) return;

        this.zombieSpawnTimer += deltaTime;
        if (this.zombieSpawnTimer >= this.zombieSpawnRate) {
            this.spawnZombie();
            this.zombieSpawnTimer = 0;
        }
    }

    spawnZombie() {
        const row = randomInt(0, this.gridRows - 1);
        const x = this.engine.canvas.width + 50;
        const y = row * this.gridHeight + this.gridOffsetY + 60;

        let zombie;
        // 随机选择僵尸类型
        if (Math.random() < 0.7) {
            zombie = new NormalZombie(x, y, row);
        } else {
            zombie = new ConeheadZombie(x, y, row);
        }

        this.zombies.push(zombie);
        this.engine.addGameObject(zombie);
        this.zombiesSpawned++;
    }

    updateNaturalSunGeneration(deltaTime) {
        this.naturalSunTimer += deltaTime;
        if (this.naturalSunTimer >= this.naturalSunRate) {
            this.generateNaturalSun();
            this.naturalSunTimer = 0;
        }
    }

    generateNaturalSun() {
        const x = random(50, this.engine.canvas.width - 50);
        const sun = Sun.createFallingSun(x);
        this.suns.push(sun);
        this.engine.addGameObject(sun);
    }

    updateWaveManagement(deltaTime) {
        // 检查当前波次是否完成
        if (this.zombiesSpawned >= this.zombiesInWave && this.zombies.length === 0) {
            if (!this.waveCompleted) {
                this.waveCompleted = true;
                this.waveTimer = 0;
            }

            this.waveTimer += deltaTime;
            if (this.waveTimer >= this.waveDelay) {
                this.startNextWave();
            }
        }
    }

    startNextWave() {
        this.wave++;
        this.zombiesInWave += 2; // 每波增加2个僵尸
        this.zombiesSpawned = 0;
        this.waveCompleted = false;

        // 增加僵尸生成速度
        this.zombieSpawnRate = Math.max(
            this.minZombieSpawnRate,
            this.zombieSpawnRate - this.zombieSpawnRateDecrease
        );
    }

    cleanupDestroyedObjects() {
        // 清理植物
        this.plants = this.plants.filter(plant => {
            if (plant.shouldDestroy) {
                // 从网格中移除
                for (let row = 0; row < this.gridRows; row++) {
                    for (let col = 0; col < this.gridCols; col++) {
                        if (this.plantGrid[row][col] === plant) {
                            this.plantGrid[row][col] = null;
                        }
                    }
                }
                return false;
            }
            return true;
        });

        // 清理僵尸
        this.zombies = this.zombies.filter(zombie => {
            if (zombie.shouldDestroy) {
                if (zombie.health <= 0) {
                    this.zombiesKilled++;
                    this.score += 10;
                }
                return false;
            }
            return true;
        });

        // 清理子弹
        this.bullets = this.bullets.filter(bullet => !bullet.shouldDestroy);

        // 清理阳光
        this.suns = this.suns.filter(sun => !sun.shouldDestroy);
    }

    zombieReachedHouse() {
        this.gameOver = true;
        this.engine.setGameState('gameOver');
        document.getElementById('gameOverScreen').classList.remove('hidden');
        this.engine.playSound('gameOver', 0.7);
    }

    // 获取指定行的僵尸
    getZombiesInRow(row) {
        return this.zombies.filter(zombie => zombie.row === row);
    }

    // 获取指定行的植物
    getPlantsInRow(row) {
        return this.plants.filter(plant => plant.gridY === row);
    }

    // 获取所有僵尸
    getAllZombies() {
        return this.zombies;
    }

    // 添加游戏对象的便捷方法
    addGameObject(obj) {
        this.engine.addGameObject(obj);

        if (obj instanceof Plant) {
            this.plants.push(obj);
        } else if (obj instanceof Zombie) {
            this.zombies.push(obj);
        } else if (obj instanceof Bullet) {
            this.bullets.push(obj);
        } else if (obj instanceof Sun) {
            this.suns.push(obj);
        }
    }

    updateUI() {
        // 更新阳光数量
        document.getElementById('sunAmount').textContent = this.sunAmount;

        // 更新植物卡片状态
        document.querySelectorAll('.plant-card').forEach(card => {
            const plantType = card.dataset.plant;
            const canAfford = this.canAffordPlant(plantType);
            const isReady = this.isPlantReady(plantType);

            if (canAfford && isReady) {
                card.classList.remove('disabled');
            } else {
                card.classList.add('disabled');
            }

            // 显示冷却时间
            if (!isReady) {
                const currentTime = Date.now() / 1000;
                const lastUsed = this.plantLastUsed[plantType];
                const cooldown = this.plantCooldowns[plantType];
                const remaining = Math.ceil(cooldown - (currentTime - lastUsed));

                if (remaining > 0) {
                    card.style.position = 'relative';
                    let cooldownOverlay = card.querySelector('.cooldown-overlay');
                    if (!cooldownOverlay) {
                        cooldownOverlay = document.createElement('div');
                        cooldownOverlay.className = 'cooldown-overlay';
                        cooldownOverlay.style.cssText = `
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: rgba(0,0,0,0.7);
                            color: white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                            border-radius: 10px;
                        `;
                        card.appendChild(cooldownOverlay);
                    }
                    cooldownOverlay.textContent = remaining;
                }
            } else {
                const cooldownOverlay = card.querySelector('.cooldown-overlay');
                if (cooldownOverlay) {
                    cooldownOverlay.remove();
                }
            }
        });
    }

    render(ctx) {
        // 绘制背景
        this.renderBackground(ctx);

        // 绘制网格（调试用）
        if (this.engine.showDebugInfo) {
            this.renderGrid(ctx);
        }

        // 绘制植物种植预览
        if (this.selectedPlant && !this.gameOver && !this.isPaused) {
            this.renderPlantPreview(ctx);
        }

        // 绘制游戏信息
        this.renderGameInfo(ctx);
    }

    renderBackground(ctx) {
        // 绘制草坪背景
        const gradient = ctx.createLinearGradient(0, 0, 0, ctx.canvas.height);
        gradient.addColorStop(0, '#87CEEB'); // 天空蓝
        gradient.addColorStop(0.3, '#98FB98'); // 浅绿
        gradient.addColorStop(1, '#228B22'); // 森林绿

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);

        // 绘制草坪行
        for (let row = 0; row < this.gridRows; row++) {
            const y = row * this.gridHeight + this.gridOffsetY;

            // 交替行颜色
            ctx.fillStyle = row % 2 === 0 ? 'rgba(34, 139, 34, 0.3)' : 'rgba(50, 205, 50, 0.3)';
            ctx.fillRect(0, y, ctx.canvas.width, this.gridHeight);

            // 绘制草地纹理
            ctx.fillStyle = 'rgba(0, 100, 0, 0.1)';
            for (let i = 0; i < 20; i++) {
                const grassX = random(0, ctx.canvas.width);
                const grassY = y + random(10, this.gridHeight - 10);
                ctx.fillRect(grassX, grassY, 2, random(5, 15));
            }
        }

        // 绘制房子轮廓
        ctx.fillStyle = 'rgba(139, 69, 19, 0.8)';
        ctx.fillRect(0, 0, 20, ctx.canvas.height);

        // 房子窗户
        ctx.fillStyle = 'rgba(255, 255, 0, 0.6)';
        for (let row = 0; row < this.gridRows; row++) {
            const windowY = row * this.gridHeight + this.gridOffsetY + 40;
            ctx.fillRect(5, windowY, 10, 20);
        }
    }

    renderGrid(ctx) {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = 1;

        // 绘制垂直线
        for (let col = 0; col <= this.gridCols; col++) {
            const x = col * this.gridWidth + this.gridOffsetX;
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, ctx.canvas.height);
            ctx.stroke();
        }

        // 绘制水平线
        for (let row = 0; row <= this.gridRows; row++) {
            const y = row * this.gridHeight + this.gridOffsetY;
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(ctx.canvas.width, y);
            ctx.stroke();
        }
    }

    renderPlantPreview(ctx) {
        const mouse = this.engine.mouse;
        const gridPos = screenToGrid(mouse.x, mouse.y);

        if (gridPos.col >= 0 && gridPos.col < this.gridCols &&
            gridPos.row >= 0 && gridPos.row < this.gridRows) {

            const screenPos = gridToScreen(gridPos.col, gridPos.row);
            const canPlace = this.plantGrid[gridPos.row][gridPos.col] === null;

            ctx.save();
            ctx.globalAlpha = 0.5;
            ctx.fillStyle = canPlace ? 'rgba(0, 255, 0, 0.3)' : 'rgba(255, 0, 0, 0.3)';
            ctx.fillRect(
                screenPos.x - this.gridWidth/2,
                screenPos.y - this.gridHeight/2,
                this.gridWidth,
                this.gridHeight
            );
            ctx.restore();

            // 绘制植物预览
            if (canPlace) {
                ctx.save();
                ctx.globalAlpha = 0.7;

                // 简单的植物预览
                const centerX = screenPos.x;
                const centerY = screenPos.y;

                switch (this.selectedPlant) {
                    case 'sunflower':
                        ctx.fillStyle = '#FFD700';
                        ctx.beginPath();
                        ctx.arc(centerX, centerY, 25, 0, Math.PI * 2);
                        ctx.fill();
                        break;
                    case 'peashooter':
                        ctx.fillStyle = '#90EE90';
                        ctx.beginPath();
                        ctx.arc(centerX, centerY, 25, 0, Math.PI * 2);
                        ctx.fill();
                        break;
                    case 'wallnut':
                        ctx.fillStyle = '#8B4513';
                        ctx.beginPath();
                        ctx.arc(centerX, centerY, 30, 0, Math.PI * 2);
                        ctx.fill();
                        break;
                }

                ctx.restore();
            }
        }
    }

    renderGameInfo(ctx) {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.font = '16px Arial';

        // 游戏信息
        const infoY = 30;
        ctx.fillStyle = 'white';
        ctx.fillText(`波次: ${this.wave}`, ctx.canvas.width - 150, infoY);
        ctx.fillText(`分数: ${this.score}`, ctx.canvas.width - 150, infoY + 25);
        ctx.fillText(`击杀: ${this.zombiesKilled}`, ctx.canvas.width - 150, infoY + 50);

        // 波次进度
        if (this.zombiesSpawned < this.zombiesInWave) {
            const progress = this.zombiesSpawned / this.zombiesInWave;
            const barWidth = 100;
            const barHeight = 10;
            const barX = ctx.canvas.width - 150;
            const barY = infoY + 75;

            // 进度条背景
            ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
            ctx.fillRect(barX, barY, barWidth, barHeight);

            // 进度条
            ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
            ctx.fillRect(barX, barY, barWidth * progress, barHeight);

            // 进度条边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 1;
            ctx.strokeRect(barX, barY, barWidth, barHeight);

            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText(`僵尸: ${this.zombiesSpawned}/${this.zombiesInWave}`, barX, barY - 5);
        }

        // 暂停提示
        if (this.isPaused) {
            ctx.save();
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('游戏暂停', ctx.canvas.width / 2, ctx.canvas.height / 2);

            ctx.font = '24px Arial';
            ctx.fillText('点击继续按钮恢复游戏', ctx.canvas.width / 2, ctx.canvas.height / 2 + 50);
            ctx.restore();
        }
    }
}

// 全局游戏实例
let game;
