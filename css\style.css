/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    overflow: hidden;
}

#gameContainer {
    position: relative;
    background: #2d5a27;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    padding: 20px;
    max-width: 1000px;
    width: 100%;
}

/* 游戏标题 */
#gameTitle {
    text-align: center;
    color: #fff;
    margin-bottom: 20px;
}

#gameTitle h1 {
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    color: #ffff00;
}

#gameTitle p {
    font-size: 1.2em;
    color: #90EE90;
}

/* 游戏界面 */
#gameUI {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 10px;
}

/* 阳光计数器 */
#sunCounter {
    display: flex;
    align-items: center;
    background: #ffff00;
    padding: 8px 15px;
    border-radius: 20px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

#sunIcon {
    width: 30px;
    height: 30px;
    margin-right: 8px;
}

#sunAmount {
    font-size: 1.5em;
    font-weight: bold;
    color: #333;
}

/* 植物选择栏 */
#plantSelector {
    display: flex;
    gap: 10px;
}

.plant-card {
    position: relative;
    width: 70px;
    height: 70px;
    background: #8B4513;
    border: 3px solid #654321;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.plant-card:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(255, 255, 0, 0.5);
}

.plant-card.selected {
    border-color: #ffff00;
    box-shadow: 0 0 15px rgba(255, 255, 0, 0.8);
}

.plant-card.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.plant-card img {
    width: 40px;
    height: 40px;
}

.plant-card .cost {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background: #ffff00;
    color: #333;
    font-size: 0.8em;
    font-weight: bold;
    padding: 2px 4px;
    border-radius: 3px;
}

/* 游戏画布 */
#gameCanvas {
    display: block;
    background: #228B22;
    border: 5px solid #8B4513;
    border-radius: 10px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

/* 游戏控制按钮 */
#gameControls {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
}

#gameControls button {
    padding: 10px 20px;
    font-size: 1.1em;
    font-weight: bold;
    color: #fff;
    background: #8B4513;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

#gameControls button:hover {
    background: #A0522D;
    transform: translateY(-2px);
}

#gameControls button:active {
    transform: translateY(0);
}

/* 游戏结束界面 */
#gameOverScreen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
}

.game-over-content {
    background: #2d5a27;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    color: #fff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.game-over-content h2 {
    font-size: 2.5em;
    color: #ff4444;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.game-over-content p {
    font-size: 1.3em;
    margin-bottom: 30px;
    color: #ffff00;
}

#playAgainBtn {
    padding: 15px 30px;
    font-size: 1.2em;
    font-weight: bold;
    color: #fff;
    background: #228B22;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

#playAgainBtn:hover {
    background: #32CD32;
    transform: translateY(-2px);
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #gameContainer {
        padding: 10px;
        margin: 10px;
    }
    
    #gameTitle h1 {
        font-size: 2em;
    }
    
    #gameCanvas {
        width: 100%;
        height: auto;
    }
    
    .plant-card {
        width: 60px;
        height: 60px;
    }
    
    .plant-card img {
        width: 35px;
        height: 35px;
    }
}
