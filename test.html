<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>植物大战僵尸 - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .test-button.danger:hover {
            background: #da190b;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <h1>植物大战僵尸 - 测试页面</h1>
    
    <div class="test-section">
        <h2>游戏预览</h2>
        <iframe src="index.html" title="植物大战僵尸游戏"></iframe>
    </div>
    
    <div class="test-section">
        <h2>功能测试</h2>
        <p>以下按钮可以测试游戏的各种功能：</p>
        
        <h3>基础功能测试</h3>
        <button class="test-button" onclick="testGameInitialization()">测试游戏初始化</button>
        <button class="test-button" onclick="testPlantSelection()">测试植物选择</button>
        <button class="test-button" onclick="testSunSystem()">测试阳光系统</button>
        <button class="test-button" onclick="testZombieSpawning()">测试僵尸生成</button>
        
        <h3>调试功能</h3>
        <button class="test-button" onclick="addTestSun()">添加阳光 (+100)</button>
        <button class="test-button" onclick="spawnTestZombie()">生成测试僵尸</button>
        <button class="test-button" onclick="clearAllZombies()">清除所有僵尸</button>
        <button class="test-button" onclick="skipToNextWave()">跳到下一波</button>
        
        <h3>游戏控制</h3>
        <button class="test-button" onclick="toggleGamePause()">暂停/继续</button>
        <button class="test-button" onclick="restartTestGame()">重新开始</button>
        <button class="test-button" onclick="toggleDebugMode()">切换调试模式</button>
        
        <h3>压力测试</h3>
        <button class="test-button danger" onclick="spawnManyZombies()">生成大量僵尸</button>
        <button class="test-button danger" onclick="plantManyPlants()">种植大量植物</button>
        <button class="test-button danger" onclick="stressTest()">综合压力测试</button>
        
        <div id="testResults"></div>
    </div>
    
    <div class="test-section">
        <h2>性能监控</h2>
        <div id="performanceInfo">
            <p>FPS: <span id="fpsDisplay">--</span></p>
            <p>游戏对象数量: <span id="objectCount">--</span></p>
            <p>内存使用: <span id="memoryUsage">--</span></p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <ul>
            <li><strong>基础功能测试</strong>：验证游戏核心功能是否正常工作</li>
            <li><strong>调试功能</strong>：快速测试游戏机制的工具</li>
            <li><strong>游戏控制</strong>：测试游戏状态控制功能</li>
            <li><strong>压力测试</strong>：测试游戏在高负载下的表现</li>
        </ul>
        
        <h3>键盘快捷键</h3>
        <ul>
            <li><code>空格键</code>：暂停/继续游戏</li>
            <li><code>Ctrl + R</code>：重新开始游戏</li>
            <li><code>1, 2, 3</code>：选择植物</li>
            <li><code>ESC</code>：取消植物选择</li>
            <li><code>Ctrl + D</code>：切换调试模式</li>
        </ul>
        
        <h3>浏览器控制台命令</h3>
        <ul>
            <li><code>addSun(amount)</code>：添加指定数量的阳光</li>
            <li><code>spawnZombie(type, row)</code>：在指定行生成僵尸</li>
            <li><code>clearZombies()</code>：清除所有僵尸</li>
            <li><code>nextWave()</code>：跳到下一波</li>
            <li><code>toggleDebugMode()</code>：切换调试模式</li>
        </ul>
    </div>

    <script>
        let testResults = document.getElementById('testResults');
        
        function addTestResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            testResults.appendChild(div);
            testResults.scrollTop = testResults.scrollHeight;
        }
        
        function getGameFromIframe() {
            try {
                return document.querySelector('iframe').contentWindow.game;
            } catch (e) {
                addTestResult('无法访问游戏实例，可能是跨域问题', 'error');
                return null;
            }
        }
        
        function testGameInitialization() {
            const game = getGameFromIframe();
            if (game && game.isInitialized) {
                addTestResult('游戏初始化测试：通过', 'success');
            } else {
                addTestResult('游戏初始化测试：失败', 'error');
            }
        }
        
        function testPlantSelection() {
            addTestResult('植物选择测试：请在游戏中点击植物卡片进行测试', 'info');
        }
        
        function testSunSystem() {
            const game = getGameFromIframe();
            if (game) {
                const initialSun = game.sunAmount;
                game.addSun(25);
                if (game.sunAmount === initialSun + 25) {
                    addTestResult('阳光系统测试：通过', 'success');
                } else {
                    addTestResult('阳光系统测试：失败', 'error');
                }
            }
        }
        
        function testZombieSpawning() {
            const game = getGameFromIframe();
            if (game) {
                const initialCount = game.zombies.length;
                game.spawnZombie();
                if (game.zombies.length > initialCount) {
                    addTestResult('僵尸生成测试：通过', 'success');
                } else {
                    addTestResult('僵尸生成测试：失败', 'error');
                }
            }
        }
        
        function addTestSun() {
            const iframe = document.querySelector('iframe');
            if (iframe.contentWindow.addSun) {
                iframe.contentWindow.addSun(100);
                addTestResult('添加了100阳光', 'success');
            }
        }
        
        function spawnTestZombie() {
            const iframe = document.querySelector('iframe');
            if (iframe.contentWindow.spawnZombie) {
                iframe.contentWindow.spawnZombie('normal', Math.floor(Math.random() * 5));
                addTestResult('生成了一个测试僵尸', 'success');
            }
        }
        
        function clearAllZombies() {
            const iframe = document.querySelector('iframe');
            if (iframe.contentWindow.clearZombies) {
                iframe.contentWindow.clearZombies();
                addTestResult('清除了所有僵尸', 'success');
            }
        }
        
        function skipToNextWave() {
            const iframe = document.querySelector('iframe');
            if (iframe.contentWindow.nextWave) {
                iframe.contentWindow.nextWave();
                addTestResult('跳到了下一波', 'success');
            }
        }
        
        function toggleGamePause() {
            const game = getGameFromIframe();
            if (game) {
                game.togglePause();
                addTestResult(`游戏${game.isPaused ? '已暂停' : '已继续'}`, 'info');
            }
        }
        
        function restartTestGame() {
            const game = getGameFromIframe();
            if (game) {
                game.restartGame();
                addTestResult('游戏已重新开始', 'info');
            }
        }
        
        function toggleDebugMode() {
            const iframe = document.querySelector('iframe');
            if (iframe.contentWindow.toggleDebugMode) {
                iframe.contentWindow.toggleDebugMode();
                addTestResult('切换了调试模式', 'info');
            }
        }
        
        function spawnManyZombies() {
            const iframe = document.querySelector('iframe');
            if (iframe.contentWindow.spawnZombie) {
                for (let i = 0; i < 20; i++) {
                    iframe.contentWindow.spawnZombie('normal', Math.floor(Math.random() * 5));
                }
                addTestResult('生成了20个僵尸进行压力测试', 'info');
            }
        }
        
        function plantManyPlants() {
            const game = getGameFromIframe();
            if (game) {
                game.addSun(5000); // 添加足够的阳光
                let planted = 0;
                for (let row = 0; row < 5; row++) {
                    for (let col = 0; col < 9; col++) {
                        if (game.plantGrid[row][col] === null && planted < 30) {
                            game.plantAt(col, row, 'peashooter');
                            planted++;
                        }
                    }
                }
                addTestResult(`种植了${planted}个植物进行压力测试`, 'info');
            }
        }
        
        function stressTest() {
            addTestResult('开始综合压力测试...', 'info');
            spawnManyZombies();
            setTimeout(() => {
                plantManyPlants();
                addTestResult('综合压力测试完成', 'success');
            }, 1000);
        }
        
        // 性能监控
        function updatePerformanceInfo() {
            const game = getGameFromIframe();
            if (game && game.engine) {
                document.getElementById('objectCount').textContent = game.engine.gameObjects.length;
                
                // 简单的FPS计算
                if (game.engine.deltaTime > 0) {
                    const fps = Math.round(1000 / game.engine.deltaTime);
                    document.getElementById('fpsDisplay').textContent = fps;
                }
                
                // 内存使用（如果支持）
                if (performance.memory) {
                    const memory = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                    document.getElementById('memoryUsage').textContent = memory + ' MB';
                }
            }
        }
        
        // 每秒更新性能信息
        setInterval(updatePerformanceInfo, 1000);
        
        addTestResult('测试页面已加载，可以开始测试', 'success');
    </script>
</body>
</html>
