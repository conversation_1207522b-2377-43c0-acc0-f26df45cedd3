// 子弹基类
class Bullet {
    constructor(x, y, damage) {
        this.x = x;
        this.y = y;
        this.damage = damage;
        this.width = 10;
        this.height = 10;
        this.speed = 300; // 像素/秒
        this.shouldDestroy = false;
        this.hasHit = false;
    }
    
    update(deltaTime) {
        // 移动子弹
        this.x += this.speed * deltaTime;
        
        // 检查是否超出屏幕
        if (this.x > 900) {
            this.shouldDestroy = true;
            return;
        }
        
        // 检查与僵尸的碰撞
        this.checkZombieCollision();
    }
    
    checkZombieCollision() {
        if (this.hasHit) return;
        
        const zombies = game.getAllZombies();
        for (let zombie of zombies) {
            if (this.checkCollisionWith(zombie)) {
                this.hitZombie(zombie);
                return;
            }
        }
    }
    
    checkCollisionWith(zombie) {
        return checkCollision(this.getBounds(), zombie.getBounds());
    }
    
    hitZombie(zombie) {
        zombie.takeDamage(this.damage);
        this.hasHit = true;
        this.shouldDestroy = true;
        
        // 添加击中效果
        this.createHitEffect();
    }
    
    createHitEffect() {
        // 创建击中粒子效果
        for (let i = 0; i < 5; i++) {
            const particle = createParticle(
                this.x + random(-5, 5),
                this.y + random(-5, 5),
                '#90EE90',
                random(2, 6),
                { 
                    x: random(-2, 2), 
                    y: random(-2, 2) 
                }
            );
            game.engine.addParticle(particle);
        }
    }
    
    render(ctx) {
        this.renderBullet(ctx);
    }
    
    renderBullet(ctx) {
        // 子类实现具体的子弹绘制
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// 豌豆子弹类
class PeaBullet extends Bullet {
    constructor(x, y, damage) {
        super(x, y, damage);
        this.width = 12;
        this.height = 12;
        this.speed = 400;
        this.rotationSpeed = 10; // 旋转速度
        this.rotation = 0;
    }
    
    update(deltaTime) {
        super.update(deltaTime);
        
        // 旋转效果
        this.rotation += this.rotationSpeed * deltaTime;
    }
    
    renderBullet(ctx) {
        ctx.save();
        
        // 移动到子弹中心
        ctx.translate(this.x + this.width/2, this.y + this.height/2);
        ctx.rotate(this.rotation);
        
        // 绘制豌豆
        ctx.fillStyle = '#90EE90';
        ctx.beginPath();
        ctx.arc(0, 0, this.width/2, 0, Math.PI * 2);
        ctx.fill();
        
        // 豌豆纹理
        ctx.fillStyle = '#228B22';
        ctx.beginPath();
        ctx.arc(-2, -2, 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(2, 2, 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(0, 3, 1.5, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(-3, 1, 1.5, 0, Math.PI * 2);
        ctx.fill();
        
        // 高光效果
        ctx.fillStyle = '#ADFF2F';
        ctx.beginPath();
        ctx.arc(-1, -3, 1, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
        
        // 绘制拖尾效果
        this.renderTrail(ctx);
    }
    
    renderTrail(ctx) {
        // 绘制子弹拖尾
        ctx.save();
        ctx.globalAlpha = 0.3;
        
        for (let i = 1; i <= 3; i++) {
            const trailX = this.x - i * 8;
            const trailAlpha = 0.3 - (i * 0.1);
            const trailSize = this.width/2 - i;
            
            if (trailSize > 0) {
                ctx.globalAlpha = trailAlpha;
                ctx.fillStyle = '#90EE90';
                ctx.beginPath();
                ctx.arc(trailX + this.width/2, this.y + this.height/2, trailSize, 0, Math.PI * 2);
                ctx.fill();
            }
        }
        
        ctx.restore();
    }
    
    createHitEffect() {
        super.createHitEffect();
        
        // 额外的豌豆爆炸效果
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const particle = createParticle(
                this.x + this.width/2,
                this.y + this.height/2,
                '#90EE90',
                random(1, 3),
                { 
                    x: Math.cos(angle) * random(1, 3), 
                    y: Math.sin(angle) * random(1, 3) 
                }
            );
            particle.decay = 0.05;
            game.engine.addParticle(particle);
        }
    }
}

// 冰豌豆子弹类（可扩展）
class IcePeaBullet extends PeaBullet {
    constructor(x, y, damage) {
        super(x, y, damage);
        this.slowEffect = 0.5; // 减速效果
        this.slowDuration = 3; // 减速持续时间
    }
    
    hitZombie(zombie) {
        super.hitZombie(zombie);
        
        // 应用冰冻效果
        if (zombie.applySlowEffect) {
            zombie.applySlowEffect(this.slowEffect, this.slowDuration);
        }
    }
    
    renderBullet(ctx) {
        ctx.save();
        
        // 移动到子弹中心
        ctx.translate(this.x + this.width/2, this.y + this.height/2);
        ctx.rotate(this.rotation);
        
        // 绘制冰豌豆
        ctx.fillStyle = '#87CEEB';
        ctx.beginPath();
        ctx.arc(0, 0, this.width/2, 0, Math.PI * 2);
        ctx.fill();
        
        // 冰晶效果
        ctx.strokeStyle = '#B0E0E6';
        ctx.lineWidth = 1;
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const x1 = Math.cos(angle) * 3;
            const y1 = Math.sin(angle) * 3;
            const x2 = Math.cos(angle) * 6;
            const y2 = Math.sin(angle) * 6;
            
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
        
        // 高光效果
        ctx.fillStyle = '#F0F8FF';
        ctx.beginPath();
        ctx.arc(-1, -3, 1, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
        
        // 绘制冰霜拖尾
        this.renderIceTrail(ctx);
    }
    
    renderIceTrail(ctx) {
        ctx.save();
        ctx.globalAlpha = 0.2;
        
        for (let i = 1; i <= 4; i++) {
            const trailX = this.x - i * 6;
            const trailAlpha = 0.2 - (i * 0.05);
            const trailSize = this.width/2 - i * 0.5;
            
            if (trailSize > 0) {
                ctx.globalAlpha = trailAlpha;
                ctx.fillStyle = '#87CEEB';
                ctx.beginPath();
                ctx.arc(trailX + this.width/2, this.y + this.height/2, trailSize, 0, Math.PI * 2);
                ctx.fill();
            }
        }
        
        ctx.restore();
    }
    
    createHitEffect() {
        // 创建冰霜爆炸效果
        for (let i = 0; i < 10; i++) {
            const particle = createParticle(
                this.x + random(-5, 5),
                this.y + random(-5, 5),
                '#87CEEB',
                random(2, 6),
                { 
                    x: random(-3, 3), 
                    y: random(-3, 3) 
                }
            );
            particle.decay = 0.03;
            game.engine.addParticle(particle);
        }
        
        // 冰晶效果
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const particle = createParticle(
                this.x + this.width/2,
                this.y + this.height/2,
                '#B0E0E6',
                random(1, 2),
                { 
                    x: Math.cos(angle) * random(2, 4), 
                    y: Math.sin(angle) * random(2, 4) 
                }
            );
            particle.decay = 0.04;
            game.engine.addParticle(particle);
        }
    }
}
