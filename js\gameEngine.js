// 游戏引擎核心类
class GameEngine {
    constructor(canvasId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.isRunning = false;
        this.isPaused = false;
        this.lastTime = 0;
        this.deltaTime = 0;
        this.fps = 60;
        this.frameInterval = 1000 / this.fps;
        
        // 游戏状态
        this.gameState = 'menu'; // menu, playing, paused, gameOver
        
        // 资源管理
        this.images = new Map();
        this.sounds = new Map();
        this.loadedResources = 0;
        this.totalResources = 0;
        
        // 输入管理
        this.mouse = {
            x: 0,
            y: 0,
            isDown: false,
            justPressed: false,
            justReleased: false
        };
        
        this.keys = new Map();
        
        // 游戏对象管理
        this.gameObjects = [];
        this.particleSystem = [];
        
        this.initEventListeners();
    }
    
    // 初始化事件监听器
    initEventListeners() {
        // 鼠标事件
        this.canvas.addEventListener('mousemove', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            this.mouse.x = e.clientX - rect.left;
            this.mouse.y = e.clientY - rect.top;
        });
        
        this.canvas.addEventListener('mousedown', (e) => {
            this.mouse.isDown = true;
            this.mouse.justPressed = true;
        });
        
        this.canvas.addEventListener('mouseup', (e) => {
            this.mouse.isDown = false;
            this.mouse.justReleased = true;
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.keys.set(e.code, true);
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys.set(e.code, false);
        });
        
        // 防止右键菜单
        this.canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
    }
    
    // 加载图片资源
    async loadImage(name, src) {
        try {
            const img = await loadImage(src);
            this.images.set(name, img);
            this.loadedResources++;
            return img;
        } catch (error) {
            console.error(`加载图片失败: ${src}`, error);
            // 创建占位图片
            const canvas = document.createElement('canvas');
            canvas.width = 50;
            canvas.height = 50;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(0, 0, 50, 50);
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Arial';
            ctx.fillText('?', 20, 30);
            this.images.set(name, canvas);
            this.loadedResources++;
        }
    }
    
    // 加载音频资源
    async loadSound(name, src) {
        try {
            const audio = await loadAudio(src);
            this.sounds.set(name, audio);
            this.loadedResources++;
            return audio;
        } catch (error) {
            console.error(`加载音频失败: ${src}`, error);
            this.loadedResources++;
        }
    }
    
    // 获取图片
    getImage(name) {
        return this.images.get(name);
    }
    
    // 获取音频
    getSound(name) {
        return this.sounds.get(name);
    }
    
    // 播放音效
    playSound(name, volume = 1) {
        const sound = this.sounds.get(name);
        if (sound) {
            playSound(sound, volume);
        }
    }
    
    // 添加游戏对象
    addGameObject(obj) {
        this.gameObjects.push(obj);
    }
    
    // 移除游戏对象
    removeGameObject(obj) {
        const index = this.gameObjects.indexOf(obj);
        if (index > -1) {
            this.gameObjects.splice(index, 1);
        }
    }
    
    // 添加粒子
    addParticle(particle) {
        this.particleSystem.push(particle);
    }
    
    // 检查键盘输入
    isKeyPressed(keyCode) {
        return this.keys.get(keyCode) || false;
    }
    
    // 开始游戏循环
    start() {
        this.isRunning = true;
        this.lastTime = performance.now();
        this.gameLoop();
    }
    
    // 停止游戏循环
    stop() {
        this.isRunning = false;
    }
    
    // 暂停游戏
    pause() {
        this.isPaused = true;
    }
    
    // 恢复游戏
    resume() {
        this.isPaused = false;
    }
    
    // 游戏主循环
    gameLoop(currentTime = performance.now()) {
        if (!this.isRunning) return;
        
        this.deltaTime = currentTime - this.lastTime;
        
        if (this.deltaTime >= this.frameInterval) {
            this.update(this.deltaTime / 1000); // 转换为秒
            this.render();
            this.lastTime = currentTime;
        }
        
        requestAnimationFrame((time) => this.gameLoop(time));
    }
    
    // 更新游戏逻辑
    update(deltaTime) {
        if (this.isPaused) return;
        
        // 更新游戏对象
        for (let i = this.gameObjects.length - 1; i >= 0; i--) {
            const obj = this.gameObjects[i];
            if (obj.update) {
                obj.update(deltaTime);
            }
            
            // 移除标记为销毁的对象
            if (obj.shouldDestroy) {
                this.gameObjects.splice(i, 1);
            }
        }
        
        // 更新粒子系统
        for (let i = this.particleSystem.length - 1; i >= 0; i--) {
            const particle = this.particleSystem[i];
            if (!updateParticle(particle)) {
                this.particleSystem.splice(i, 1);
            }
        }
        
        // 重置鼠标状态
        this.mouse.justPressed = false;
        this.mouse.justReleased = false;
    }
    
    // 渲染游戏画面
    render() {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制游戏对象
        this.gameObjects.forEach(obj => {
            if (obj.render) {
                obj.render(this.ctx);
            }
        });
        
        // 绘制粒子
        this.particleSystem.forEach(particle => {
            drawParticle(this.ctx, particle);
        });
        
        // 绘制调试信息
        if (this.showDebugInfo) {
            this.renderDebugInfo();
        }
    }
    
    // 渲染调试信息
    renderDebugInfo() {
        this.ctx.fillStyle = 'white';
        this.ctx.font = '16px Arial';
        this.ctx.fillText(`FPS: ${Math.round(1000 / this.deltaTime)}`, 10, 30);
        this.ctx.fillText(`Objects: ${this.gameObjects.length}`, 10, 50);
        this.ctx.fillText(`Particles: ${this.particleSystem.length}`, 10, 70);
        this.ctx.fillText(`Mouse: ${Math.round(this.mouse.x)}, ${Math.round(this.mouse.y)}`, 10, 90);
    }
    
    // 清空所有游戏对象
    clearGameObjects() {
        this.gameObjects = [];
        this.particleSystem = [];
    }
    
    // 设置游戏状态
    setGameState(state) {
        this.gameState = state;
    }
    
    // 获取游戏状态
    getGameState() {
        return this.gameState;
    }
}
