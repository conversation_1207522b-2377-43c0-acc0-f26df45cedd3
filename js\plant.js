// 植物基类
class Plant {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.width = 80;
        this.height = 80;
        this.health = 100;
        this.maxHealth = 100;
        this.cost = 50;
        this.rechargeTime = 0;
        this.isReady = true;
        this.shouldDestroy = false;
        this.animationFrame = 0;
        this.animationSpeed = 0.1;
        this.lastActionTime = 0;
        
        // 网格位置
        this.gridX = Math.floor(x / 100);
        this.gridY = Math.floor(y / 120);
    }
    
    update(deltaTime) {
        // 更新动画
        this.animationFrame += this.animationSpeed;
        
        // 更新充能时间
        if (this.rechargeTime > 0) {
            this.rechargeTime -= deltaTime;
            if (this.rechargeTime <= 0) {
                this.isReady = true;
            }
        }
        
        // 检查是否应该销毁
        if (this.health <= 0) {
            this.shouldDestroy = true;
        }
    }
    
    render(ctx) {
        // 绘制植物阴影
        ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        ctx.ellipse(this.x + this.width/2, this.y + this.height + 5, 
                   this.width/2, 10, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制植物主体
        this.renderPlant(ctx);
        
        // 绘制生命值条
        if (this.health < this.maxHealth) {
            this.renderHealthBar(ctx);
        }
    }
    
    renderPlant(ctx) {
        // 子类实现具体的植物绘制
    }
    
    renderHealthBar(ctx) {
        const barWidth = this.width;
        const barHeight = 6;
        const barX = this.x;
        const barY = this.y - 15;
        
        // 背景
        ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
        ctx.fillRect(barX, barY, barWidth, barHeight);
        
        // 生命值
        ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
        const healthWidth = (this.health / this.maxHealth) * barWidth;
        ctx.fillRect(barX, barY, healthWidth, barHeight);
        
        // 边框
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.lineWidth = 1;
        ctx.strokeRect(barX, barY, barWidth, barHeight);
    }
    
    takeDamage(damage) {
        this.health -= damage;
        if (this.health < 0) this.health = 0;
    }
    
    canPlaceAt(gridX, gridY) {
        return true; // 大部分植物可以种植在任何地方
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// 向日葵类
class Sunflower extends Plant {
    constructor(x, y) {
        super(x, y, 'sunflower');
        this.cost = 50;
        this.health = 100;
        this.maxHealth = 100;
        this.sunProductionRate = 25; // 每25秒产生一个阳光
        this.lastSunTime = 0;
    }
    
    update(deltaTime) {
        super.update(deltaTime);
        
        // 生产阳光
        this.lastSunTime += deltaTime;
        if (this.lastSunTime >= this.sunProductionRate) {
            this.produceSun();
            this.lastSunTime = 0;
        }
    }
    
    produceSun() {
        // 创建阳光对象
        const sun = new Sun(this.x + this.width/2, this.y + this.height/2, 25);
        game.addGameObject(sun);
        
        // 添加粒子效果
        for (let i = 0; i < 5; i++) {
            const particle = createParticle(
                this.x + this.width/2 + random(-10, 10),
                this.y + this.height/2 + random(-10, 10),
                '#ffff00',
                random(3, 8),
                { x: random(-1, 1), y: random(-2, -0.5) }
            );
            game.engine.addParticle(particle);
        }
    }
    
    renderPlant(ctx) {
        // 绘制向日葵
        const centerX = this.x + this.width/2;
        const centerY = this.y + this.height/2;
        
        // 茎
        ctx.fillStyle = '#228B22';
        ctx.fillRect(centerX - 5, centerY, 10, this.height/2);
        
        // 叶子
        ctx.fillStyle = '#32CD32';
        ctx.beginPath();
        ctx.ellipse(centerX - 15, centerY + 10, 8, 15, -0.5, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.ellipse(centerX + 15, centerY + 15, 8, 15, 0.5, 0, Math.PI * 2);
        ctx.fill();
        
        // 花盘
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(centerX, centerY - 10, 25, 0, Math.PI * 2);
        ctx.fill();
        
        // 花瓣
        ctx.fillStyle = '#FFA500';
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const petalX = centerX + Math.cos(angle) * 20;
            const petalY = centerY - 10 + Math.sin(angle) * 20;
            ctx.beginPath();
            ctx.ellipse(petalX, petalY, 8, 15, angle, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // 花心
        ctx.fillStyle = '#8B4513';
        ctx.beginPath();
        ctx.arc(centerX, centerY - 10, 12, 0, Math.PI * 2);
        ctx.fill();
        
        // 种子点
        ctx.fillStyle = '#654321';
        for (let i = 0; i < 12; i++) {
            const angle = (i / 12) * Math.PI * 2;
            const seedX = centerX + Math.cos(angle) * 8;
            const seedY = centerY - 10 + Math.sin(angle) * 8;
            ctx.beginPath();
            ctx.arc(seedX, seedY, 2, 0, Math.PI * 2);
            ctx.fill();
        }
    }
}

// 豌豆射手类
class Peashooter extends Plant {
    constructor(x, y) {
        super(x, y, 'peashooter');
        this.cost = 100;
        this.health = 100;
        this.maxHealth = 100;
        this.shootRate = 1.5; // 每1.5秒射击一次
        this.lastShootTime = 0;
        this.damage = 20;
        this.range = 800; // 射程
    }
    
    update(deltaTime) {
        super.update(deltaTime);
        
        // 检查是否有僵尸在射程内
        this.lastShootTime += deltaTime;
        if (this.lastShootTime >= this.shootRate) {
            if (this.hasZombieInRange()) {
                this.shoot();
                this.lastShootTime = 0;
            }
        }
    }
    
    hasZombieInRange() {
        // 检查同一行是否有僵尸
        const zombies = game.getZombiesInRow(this.gridY);
        return zombies.some(zombie => zombie.x > this.x && zombie.x - this.x <= this.range);
    }
    
    shoot() {
        // 创建豌豆子弹
        const bullet = new PeaBullet(
            this.x + this.width,
            this.y + this.height/2,
            this.damage
        );
        game.addGameObject(bullet);
        
        // 播放射击音效
        game.engine.playSound('shoot', 0.3);
        
        // 添加射击粒子效果
        for (let i = 0; i < 3; i++) {
            const particle = createParticle(
                this.x + this.width,
                this.y + this.height/2 + random(-5, 5),
                '#90EE90',
                random(2, 5),
                { x: random(2, 4), y: random(-1, 1) }
            );
            game.engine.addParticle(particle);
        }
    }
    
    renderPlant(ctx) {
        // 绘制豌豆射手
        const centerX = this.x + this.width/2;
        const centerY = this.y + this.height/2;
        
        // 茎
        ctx.fillStyle = '#228B22';
        ctx.fillRect(centerX - 5, centerY + 10, 10, this.height/2 - 10);
        
        // 叶子
        ctx.fillStyle = '#32CD32';
        ctx.beginPath();
        ctx.ellipse(centerX - 20, centerY + 15, 10, 18, -0.3, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.ellipse(centerX + 20, centerY + 20, 10, 18, 0.3, 0, Math.PI * 2);
        ctx.fill();
        
        // 身体
        ctx.fillStyle = '#90EE90';
        ctx.beginPath();
        ctx.arc(centerX, centerY, 25, 0, Math.PI * 2);
        ctx.fill();
        
        // 嘴巴/炮管
        ctx.fillStyle = '#228B22';
        ctx.fillRect(centerX + 20, centerY - 8, 25, 16);
        ctx.beginPath();
        ctx.arc(centerX + 45, centerY, 8, 0, Math.PI * 2);
        ctx.fill();
        
        // 眼睛
        ctx.fillStyle = '#000000';
        ctx.beginPath();
        ctx.arc(centerX - 8, centerY - 8, 4, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(centerX + 8, centerY - 8, 4, 0, Math.PI * 2);
        ctx.fill();
        
        // 眼睛高光
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(centerX - 6, centerY - 10, 2, 0, Math.PI * 2);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(centerX + 10, centerY - 10, 2, 0, Math.PI * 2);
        ctx.fill();
    }
}

// 坚果墙类
class Wallnut extends Plant {
    constructor(x, y) {
        super(x, y, 'wallnut');
        this.cost = 50;
        this.health = 300;
        this.maxHealth = 300;
    }
    
    renderPlant(ctx) {
        // 绘制坚果墙
        const centerX = this.x + this.width/2;
        const centerY = this.y + this.height/2;
        
        // 坚果外壳
        ctx.fillStyle = '#8B4513';
        ctx.beginPath();
        ctx.arc(centerX, centerY, 30, 0, Math.PI * 2);
        ctx.fill();
        
        // 裂纹效果（根据生命值）
        const damageLevel = 1 - (this.health / this.maxHealth);
        if (damageLevel > 0.3) {
            ctx.strokeStyle = '#654321';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(centerX - 15, centerY - 10);
            ctx.lineTo(centerX + 10, centerY + 15);
            ctx.stroke();
        }
        if (damageLevel > 0.6) {
            ctx.beginPath();
            ctx.moveTo(centerX + 15, centerY - 15);
            ctx.lineTo(centerX - 10, centerY + 10);
            ctx.stroke();
        }
        
        // 坚果纹理
        ctx.fillStyle = '#A0522D';
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const x = centerX + Math.cos(angle) * 15;
            const y = centerY + Math.sin(angle) * 15;
            ctx.beginPath();
            ctx.arc(x, y, 3, 0, Math.PI * 2);
            ctx.fill();
        }
        
        // 眼睛（如果生命值较高）
        if (this.health > this.maxHealth * 0.5) {
            ctx.fillStyle = '#000000';
            ctx.beginPath();
            ctx.arc(centerX - 8, centerY - 5, 3, 0, Math.PI * 2);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(centerX + 8, centerY - 5, 3, 0, Math.PI * 2);
            ctx.fill();
        }
    }
}
