// 阳光类
class Sun {
    constructor(x, y, value = 25) {
        this.x = x;
        this.y = y;
        this.value = value;
        this.width = 30;
        this.height = 30;
        this.shouldDestroy = false;
        this.isCollected = false;
        
        // 动画属性
        this.animationFrame = 0;
        this.animationSpeed = 0.1;
        this.pulseScale = 1;
        this.pulseDirection = 1;
        
        // 移动属性
        this.targetX = x;
        this.targetY = y;
        this.moveSpeed = 200;
        this.isMovingToTarget = false;
        
        // 生命周期
        this.lifeTime = 10; // 10秒后消失
        this.currentLifeTime = 0;
        this.fadeStartTime = 8; // 8秒后开始闪烁
        
        // 收集动画
        this.collectAnimationTime = 0;
        this.collectAnimationDuration = 0.5;
        
        // 随机初始偏移
        this.offsetX = random(-10, 10);
        this.offsetY = random(-10, 10);
        this.bobSpeed = random(2, 4);
        this.bobAmount = random(5, 10);
    }
    
    update(deltaTime) {
        this.currentLifeTime += deltaTime;
        this.animationFrame += this.animationSpeed;
        
        if (this.isCollected) {
            this.updateCollectAnimation(deltaTime);
        } else {
            this.updateNormalBehavior(deltaTime);
        }
        
        // 检查是否应该销毁
        if (this.currentLifeTime >= this.lifeTime && !this.isCollected) {
            this.shouldDestroy = true;
        }
    }
    
    updateNormalBehavior(deltaTime) {
        // 脉冲动画
        this.pulseScale += this.pulseDirection * 0.5 * deltaTime;
        if (this.pulseScale >= 1.2) {
            this.pulseScale = 1.2;
            this.pulseDirection = -1;
        } else if (this.pulseScale <= 0.8) {
            this.pulseScale = 0.8;
            this.pulseDirection = 1;
        }
        
        // 上下浮动效果
        this.offsetY = Math.sin(this.animationFrame * this.bobSpeed) * this.bobAmount;
        
        // 检查鼠标点击
        this.checkMouseClick();
    }
    
    updateCollectAnimation(deltaTime) {
        this.collectAnimationTime += deltaTime;
        
        if (this.collectAnimationTime >= this.collectAnimationDuration) {
            this.shouldDestroy = true;
            return;
        }
        
        // 移动到阳光计数器
        const sunCounter = document.getElementById('sunCounter');
        const rect = sunCounter.getBoundingClientRect();
        const canvasRect = game.engine.canvas.getBoundingClientRect();
        
        this.targetX = rect.left - canvasRect.left + rect.width / 2;
        this.targetY = rect.top - canvasRect.top + rect.height / 2;
        
        // 平滑移动到目标位置
        const progress = this.collectAnimationTime / this.collectAnimationDuration;
        const easeProgress = easeInOut(progress);
        
        this.x = lerp(this.x, this.targetX, easeProgress);
        this.y = lerp(this.y, this.targetY, easeProgress);
        
        // 缩放效果
        this.pulseScale = lerp(1, 0.5, progress);
    }
    
    checkMouseClick() {
        const mouse = game.engine.mouse;
        
        if (mouse.justPressed) {
            const bounds = this.getBounds();
            if (isPointInRect(mouse.x, mouse.y, bounds)) {
                this.collect();
            }
        }
    }
    
    collect() {
        if (this.isCollected) return;
        
        this.isCollected = true;
        this.collectAnimationTime = 0;
        
        // 增加阳光数量
        game.addSun(this.value);
        
        // 播放收集音效
        game.engine.playSound('collectSun', 0.5);
        
        // 创建收集粒子效果
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const particle = createParticle(
                this.x + this.width/2,
                this.y + this.height/2,
                '#FFD700',
                random(2, 5),
                {
                    x: Math.cos(angle) * random(1, 3),
                    y: Math.sin(angle) * random(1, 3)
                }
            );
            particle.decay = 0.04;
            game.engine.addParticle(particle);
        }
    }
    
    render(ctx) {
        ctx.save();
        
        // 计算实际位置
        const renderX = this.x + this.offsetX;
        const renderY = this.y + this.offsetY;
        
        // 移动到阳光中心
        ctx.translate(renderX + this.width/2, renderY + this.height/2);
        ctx.scale(this.pulseScale, this.pulseScale);
        
        // 闪烁效果（生命周期末期）
        let alpha = 1;
        if (this.currentLifeTime >= this.fadeStartTime && !this.isCollected) {
            const fadeProgress = (this.currentLifeTime - this.fadeStartTime) / (this.lifeTime - this.fadeStartTime);
            alpha = 0.3 + 0.7 * Math.sin(fadeProgress * Math.PI * 10);
        }
        
        ctx.globalAlpha = alpha;
        
        // 绘制阳光光晕
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.width);
        gradient.addColorStop(0, 'rgba(255, 255, 0, 0.8)');
        gradient.addColorStop(0.7, 'rgba(255, 215, 0, 0.4)');
        gradient.addColorStop(1, 'rgba(255, 215, 0, 0)');
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, this.width, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制阳光主体
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(0, 0, this.width/2, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制阳光射线
        ctx.strokeStyle = '#FFA500';
        ctx.lineWidth = 2;
        const rayCount = 8;
        const rayLength = this.width/2 + 5;
        
        for (let i = 0; i < rayCount; i++) {
            const angle = (i / rayCount) * Math.PI * 2 + this.animationFrame * 0.5;
            const x1 = Math.cos(angle) * (this.width/2 - 2);
            const y1 = Math.sin(angle) * (this.width/2 - 2);
            const x2 = Math.cos(angle) * rayLength;
            const y2 = Math.sin(angle) * rayLength;
            
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
        
        // 绘制阳光表情
        if (!this.isCollected) {
            // 眼睛
            ctx.fillStyle = '#FF8C00';
            ctx.beginPath();
            ctx.arc(-5, -3, 2, 0, Math.PI * 2);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(5, -3, 2, 0, Math.PI * 2);
            ctx.fill();
            
            // 嘴巴
            ctx.strokeStyle = '#FF8C00';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(0, 2, 6, 0, Math.PI);
            ctx.stroke();
        }
        
        // 绘制数值
        if (this.value > 0) {
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.strokeStyle = '#000000';
            ctx.lineWidth = 2;
            ctx.strokeText(this.value.toString(), 0, 8);
            ctx.fillText(this.value.toString(), 0, 8);
        }
        
        ctx.restore();
        
        // 绘制收集提示
        if (!this.isCollected && this.isMouseOver()) {
            this.renderCollectHint(ctx);
        }
    }
    
    renderCollectHint(ctx) {
        ctx.save();
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        
        const hintText = '点击收集';
        const textWidth = ctx.measureText(hintText).width;
        const hintX = this.x + this.width/2;
        const hintY = this.y - 20;
        
        // 绘制提示背景
        ctx.fillRect(hintX - textWidth/2 - 5, hintY - 15, textWidth + 10, 20);
        
        // 绘制提示文字
        ctx.fillStyle = '#FFFFFF';
        ctx.fillText(hintText, hintX, hintY - 5);
        
        ctx.restore();
    }
    
    isMouseOver() {
        const mouse = game.engine.mouse;
        const bounds = this.getBounds();
        return isPointInRect(mouse.x, mouse.y, bounds);
    }
    
    getBounds() {
        return {
            x: this.x + this.offsetX,
            y: this.y + this.offsetY,
            width: this.width,
            height: this.height
        };
    }
    
    // 静态方法：创建从天空掉落的阳光
    static createFallingSun(x) {
        const sun = new Sun(x, -50, 25);
        sun.targetY = random(100, 400);
        sun.isMovingToTarget = true;
        sun.fallSpeed = random(50, 100);
        
        // 重写update方法以实现掉落效果
        const originalUpdate = sun.update.bind(sun);
        sun.update = function(deltaTime) {
            if (this.isMovingToTarget && !this.isCollected) {
                this.y += this.fallSpeed * deltaTime;
                if (this.y >= this.targetY) {
                    this.y = this.targetY;
                    this.isMovingToTarget = false;
                }
            }
            originalUpdate(deltaTime);
        };
        
        return sun;
    }
}
