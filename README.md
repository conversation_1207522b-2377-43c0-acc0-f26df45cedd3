# 植物大战僵尸 - 网页版

一个使用HTML5 Canvas和JavaScript开发的植物大战僵尸网页游戏。

## 功能特性

### 已实现功能
- ✅ 完整的游戏引擎系统
- ✅ 9x5网格游戏地图
- ✅ 三种基础植物：向日葵、豌豆射手、坚果墙
- ✅ 两种僵尸：普通僵尸、路障僵尸
- ✅ 阳光系统和资源管理
- ✅ 子弹系统和碰撞检测
- ✅ 粒子效果系统
- ✅ 波次系统
- ✅ 游戏界面和HUD
- ✅ 暂停/继续功能
- ✅ 键盘快捷键支持
- ✅ 调试模式

### 植物介绍
1. **向日葵** (50阳光)
   - 每25秒产生25阳光
   - 生命值：100
   - 冷却时间：7.5秒

2. **豌豆射手** (100阳光)
   - 每1.5秒发射一颗豌豆
   - 伤害：20
   - 生命值：100
   - 冷却时间：7.5秒

3. **坚果墙** (50阳光)
   - 高血量防御植物
   - 生命值：300
   - 冷却时间：30秒

### 僵尸介绍
1. **普通僵尸**
   - 生命值：100
   - 移动速度：20像素/秒
   - 攻击力：50

2. **路障僵尸**
   - 生命值：200
   - 移动速度：18像素/秒
   - 攻击力：50
   - 特殊：带有路障头盔

## 如何运行

### 方法一：直接打开HTML文件
1. 下载所有文件到本地文件夹
2. 双击 `index.html` 文件
3. 在浏览器中开始游戏

### 方法二：使用本地服务器（推荐）
1. 安装Python（如果没有的话）
2. 在项目文件夹中打开命令行
3. 运行以下命令之一：
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```
4. 在浏览器中访问 `http://localhost:8000`

### 方法三：使用Node.js服务器
1. 安装Node.js
2. 全局安装http-server：`npm install -g http-server`
3. 在项目文件夹中运行：`http-server`
4. 在浏览器中访问显示的地址

## 游戏操作

### 鼠标操作
- 点击植物卡片选择植物
- 点击游戏区域种植植物
- 点击阳光收集阳光
- 点击按钮控制游戏

### 键盘快捷键
- `空格键`：暂停/继续游戏
- `Ctrl + R`：重新开始游戏
- `1`：选择向日葵
- `2`：选择豌豆射手
- `3`：选择坚果墙
- `ESC`：取消植物选择
- `Ctrl + D`：切换调试模式

### 调试命令（在浏览器控制台中使用）
- `toggleDebugMode()`：切换调试模式
- `addSun(amount)`：添加阳光
- `spawnZombie(type, row)`：生成僵尸
- `clearZombies()`：清除所有僵尸
- `nextWave()`：跳到下一波

## 游戏规则

1. **目标**：阻止僵尸到达房子
2. **阳光**：游戏开始时有999999阳光，可以自由种植植物
3. **植物**：不同植物有不同的功能和成本
4. **僵尸**：会从右侧不断出现，向左移动
5. **波次**：每波僵尸数量会增加，生成速度会加快

## 技术实现

### 技术栈
- HTML5 Canvas
- JavaScript ES6+
- CSS3

### 架构设计
- **GameEngine**：游戏引擎核心
- **Game**：主游戏逻辑
- **Plant/Zombie/Bullet**：游戏对象类
- **Utils**：工具函数库

### 文件结构
```
├── index.html          # 主HTML文件
├── css/
│   └── style.css       # 样式文件
├── js/
│   ├── utils.js        # 工具函数
│   ├── gameEngine.js   # 游戏引擎
│   ├── plant.js        # 植物类
│   ├── zombie.js       # 僵尸类
│   ├── bullet.js       # 子弹类
│   ├── sun.js          # 阳光类
│   ├── game.js         # 主游戏逻辑
│   └── main.js         # 入口文件
├── images/             # 图片资源（占位）
├── audio/              # 音频资源（占位）
└── README.md           # 说明文档
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发计划

### 待实现功能
- [ ] 更多植物类型
- [ ] 更多僵尸类型
- [ ] 关卡系统
- [ ] 成就系统
- [ ] 本地存档
- [ ] 音效和背景音乐
- [ ] 移动端适配

## 许可证

本项目仅供学习和娱乐使用。

## 贡献

欢迎提交Issue和Pull Request来改进游戏！

---

享受游戏吧！🌻🧟‍♂️
